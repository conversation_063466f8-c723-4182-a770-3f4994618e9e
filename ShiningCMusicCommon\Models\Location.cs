using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public class Location
    {
        public int LocationId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string LocationName { get; set; } = string.Empty;
        
        // Navigation properties
        public virtual ICollection<Lesson> Lessons { get; set; } = new List<Lesson>();
    }
}
