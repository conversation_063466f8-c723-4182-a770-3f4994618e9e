# Test the configuration endpoint
param(
    [string]$BaseUrl = "https://shiningcmusicapp.azurewebsites.net"
)

Write-Host "Testing configuration endpoint..." -ForegroundColor Green

try {
    $configUrl = "$BaseUrl/api/configuration"
    Write-Host "Calling: $configUrl" -ForegroundColor Yellow
    
    $response = Invoke-RestMethod -Uri $configUrl -Method GET -ContentType "application/json"
    
    Write-Host "Configuration Response:" -ForegroundColor Green
    Write-Host "ApiBaseUrl: $($response.ApiBaseUrl)" -ForegroundColor Cyan
    Write-Host "SyncfusionLicense: $($response.SyncfusionLicense.Substring(0, 20))..." -ForegroundColor Cyan
    
    if ($response.ApiBaseUrl -like "*azurewebsites.net*") {
        Write-Host "✓ Configuration is correctly using Azure URL" -ForegroundColor Green
    } else {
        Write-Host "✗ Configuration is not using Azure URL" -ForegroundColor Red
    }
    
} catch {
    Write-Host "✗ Error testing configuration endpoint: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Test completed." -ForegroundColor Green
