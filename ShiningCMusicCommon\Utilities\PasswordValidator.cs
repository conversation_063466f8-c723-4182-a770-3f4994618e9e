namespace ShiningCMusicCommon.Utilities
{
    public static class PasswordValidator
    {
        public static bool IsValidPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                return false;

            // Minimum length requirement
            if (password.Length < 8)
                return false;

            // Add more validation rules as needed:
            // - At least one uppercase letter
            // - At least one lowercase letter  
            // - At least one digit
            // - At least one special character
            
            return true;
        }

        public static string GetPasswordRequirements()
        {
            return "Password must be at least 8 characters long.";
        }

        public static List<string> GetPasswordErrors(string password)
        {
            var errors = new List<string>();

            if (string.IsNullOrEmpty(password))
            {
                errors.Add("Password is required.");
                return errors;
            }

            if (password.Length < 8)
            {
                errors.Add("Password must be at least 8 characters long.");
            }

            // Add more specific validation rules as needed
            // if (!password.Any(char.IsUpper))
            // {
            //     errors.Add("Password must contain at least one uppercase letter.");
            // }

            // if (!password.Any(char.<PERSON><PERSON><PERSON><PERSON>))
            // {
            //     errors.Add("Password must contain at least one lowercase letter.");
            // }

            // if (!password.Any(char.IsDigit))
            // {
            //     errors.Add("Password must contain at least one number.");
            // }

            return errors;
        }

        /// <summary>
        /// Validates password and returns the first error message, or null if valid
        /// Useful for simple validation scenarios
        /// </summary>
        public static string? GetFirstPasswordError(string password)
        {
            var errors = GetPasswordErrors(password);
            return errors.FirstOrDefault();
        }

        /// <summary>
        /// Validates password strength and returns a score from 0-100
        /// Can be used for password strength meters
        /// </summary>
        public static int GetPasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password))
                return 0;

            int score = 0;

            // Length scoring
            if (password.Length >= 8) score += 25;
            if (password.Length >= 12) score += 15;
            if (password.Length >= 16) score += 10;

            // Character variety scoring
            if (password.Any(char.IsUpper)) score += 15;
            if (password.Any(char.IsLower)) score += 15;
            if (password.Any(char.IsDigit)) score += 15;
            if (password.Any(c => !char.IsLetterOrDigit(c))) score += 15;

            return Math.Min(score, 100);
        }
    }
}
