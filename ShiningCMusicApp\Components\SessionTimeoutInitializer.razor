@using ShiningCMusicApp.Services
@inject ISessionTimeoutService SessionTimeoutService
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject IJSRuntime JSRuntime
@implements IDisposable

@code {
    private bool _initialized = false;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && !_initialized)
        {
            try
            {
                // Initialize the session timeout service
                await SessionTimeoutService.InitializeAsync();
                
                // Set up the connection between auth provider and session timeout service
                AuthStateProvider.SetSessionTimeoutService(SessionTimeoutService);
                
                // Check if user is already authenticated and start timeout if needed
                var authState = await AuthStateProvider.GetAuthenticationStateAsync();
                if (authState.User.Identity?.IsAuthenticated == true)
                {
                    await SessionTimeoutService.StartTimeoutAsync();
                    Console.WriteLine("Session timeout started for existing authenticated user");
                }
                
                _initialized = true;
                Console.WriteLine("Session timeout initializer completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing session timeout: {ex.Message}");
            }
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
