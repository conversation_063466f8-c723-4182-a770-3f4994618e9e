using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Interfaces
{
    public interface ILocationService
    {
        Task<IEnumerable<Location>> GetLocationsAsync();
        Task<Location?> GetLocationAsync(int id);
        Task<Location> CreateLocationAsync(Location location);
        Task<bool> UpdateLocationAsync(int id, Location location);
        Task<bool> DeleteLocationAsync(int id);
    }
}
