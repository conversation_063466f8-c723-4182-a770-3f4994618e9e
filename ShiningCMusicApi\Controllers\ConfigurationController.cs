using Microsoft.AspNetCore.Mvc;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ConfigurationController : ControllerBase
    {
        private readonly IConfiguration _configuration;

        public ConfigurationController(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        [HttpGet]
        public IActionResult GetConfiguration()
        {
            try
            {
                // Get API base URL from environment variable or configuration
                var apiBaseUrl = Environment.GetEnvironmentVariable("API_BASE_URL")
                    ?? _configuration["ApiBaseUrl"]
                    ?? "https://localhost:7268/";

                // Ensure it ends with /
                if (!apiBaseUrl.EndsWith("/"))
                {
                    apiBaseUrl += "/";
                }

                // Get session timeout from environment variable or configuration
                var sessionTimeoutMinutes = 30; // Default value
                if (int.TryParse(Environment.GetEnvironmentVariable("SESSION_TIMEOUT_MINUTES"), out var envTimeout))
                {
                    sessionTimeoutMinutes = envTimeout;
                }
                else if (int.TryParse(_configuration["SessionTimeout:TimeoutMinutes"], out var configTimeout))
                {
                    sessionTimeoutMinutes = configTimeout;
                }

                var config = new AppConfiguration
                {
                    ApiBaseUrl = $"{apiBaseUrl}api",
                    SyncfusionLicense = _configuration["SyncfusionLicense"]
                        ?? Environment.GetEnvironmentVariable("SYNCFUSION_LICENSE")
                        ?? "MzkxNzI4MkAzMjM5MmUzMDJlMzAzYjMyMzkzYmlCSHVjUFNHMVcwWDI0Mm5reFc2M21MbXA4cEFVcWRRWXl1eFFUWnlXYTA9",
                    SessionTimeoutMinutes = sessionTimeoutMinutes
                };

                return Ok(config);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to get configuration", details = ex.Message });
            }
        }
    }
}
