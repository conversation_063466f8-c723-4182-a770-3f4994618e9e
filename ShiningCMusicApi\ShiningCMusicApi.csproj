<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.15" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="IdentityServer4" Version="4.1.2" />
    <PackageReference Include="IdentityServer4.AccessTokenValidation" Version="3.0.1" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
	<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.15" />
	<PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ShiningCMusicApp\ShiningCMusicApp.csproj" />
    <ProjectReference Include="..\ShiningCMusicCommon\ShiningCMusicCommon.csproj" />
  </ItemGroup>

</Project>
