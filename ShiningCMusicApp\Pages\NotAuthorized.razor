@page "/not-authorized"
@using ShiningCMusicApp.Services
@inject NavigationManager Navigation
@inject CustomAuthenticationStateProvider AuthStateProvider

<PageTitle>Access Denied</PageTitle>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i> Access Denied
                    </h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-lock fa-4x text-warning"></i>
                    </div>
                    <h5 class="card-title">You don't have permission to access this page</h5>
                    <p class="card-text text-muted">
                        The page you're trying to access requires different permissions than your current role allows.
                    </p>
                    
                    <div class="mt-4">
                        <AuthorizeView Roles="Administrator">
                            <button class="btn btn-primary me-2" @onclick="GoToHome">
                                <i class="fas fa-home"></i> Go to Home
                            </button>
                        </AuthorizeView>
                        
                        <AuthorizeView Roles="Tutor,Student">
                            <button class="btn btn-primary me-2" @onclick="GoToLessons">
                                <i class="fas fa-calendar"></i> Go to My Lessons
                            </button>
                        </AuthorizeView>
                        
@*                         <button class="btn btn-outline-secondary" @onclick="GoBack">
                            <i class="fas fa-arrow-left"></i> Go Back
                        </button> *@
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="text-muted">
                        <small>
                            <strong>Need different access?</strong><br>
                            Contact your administrator if you believe you should have access to this page.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private void GoToHome()
    {
        Navigation.NavigateTo("/", true);
    }
    
    private void GoToLessons()
    {
        Navigation.NavigateTo("/lessons", true);
    }
    
    // private void GoBack()
    // {
    //     Navigation.NavigateTo("javascript:history.back()", true);
    // }
}
