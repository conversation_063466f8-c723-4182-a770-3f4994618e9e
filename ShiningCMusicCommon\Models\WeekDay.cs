using ShiningCMusicCommon.Enums;
using ShiningCMusicCommon.Constants;
using ShiningCMusicCommon.Extensions;

namespace ShiningCMusicCommon.Models
{
    /// <summary>
    /// Represents a day of the week for recurrence patterns
    /// </summary>
    public class WeekDay
    {
        /// <summary>
        /// RFC 5545 compliant day code (e.g., "MO", "TU", etc.)
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Display name for the day (e.g., "Mon", "Tue", etc.)
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Whether this day is selected in the recurrence pattern
        /// </summary>
        public bool IsSelected { get; set; }

        /// <summary>
        /// The corresponding WeekDayType enum value
        /// </summary>
        public WeekDayType WeekDayType { get; set; }

        /// <summary>
        /// Default constructor
        /// </summary>
        public WeekDay()
        {
        }

        /// <summary>
        /// Constructor with WeekDayType
        /// </summary>
        /// <param name="weekDayType">The week day type</param>
        /// <param name="isSelected">Whether the day is initially selected</param>
        public WeekDay(WeekDayType weekDayType, bool isSelected = false)
        {
            WeekDayType = weekDayType;
            Code = weekDayType.ToCode();
            Name = weekDayType.ToDisplayName();
            IsSelected = isSelected;
        }

        /// <summary>
        /// Constructor with code and name
        /// </summary>
        /// <param name="code">RFC 5545 day code</param>
        /// <param name="name">Display name</param>
        /// <param name="isSelected">Whether the day is initially selected</param>
        public WeekDay(string code, string name, bool isSelected = false)
        {
            Code = code;
            Name = name;
            IsSelected = isSelected;
            WeekDayType = WeekDayExtensions.FromCode(code);
        }

        /// <summary>
        /// Creates a list of all week days with default values
        /// </summary>
        /// <returns>List of WeekDay objects for all days of the week</returns>
        public static List<WeekDay> CreateWeekDaysList()
        {
            return new List<WeekDay>
            {
                new WeekDay(WeekDayType.Monday),
                new WeekDay(WeekDayType.Tuesday),
                new WeekDay(WeekDayType.Wednesday),
                new WeekDay(WeekDayType.Thursday),
                new WeekDay(WeekDayType.Friday),
                new WeekDay(WeekDayType.Saturday),
                new WeekDay(WeekDayType.Sunday)
            };
        }

        /// <summary>
        /// Creates a list of week days with a specific day selected
        /// </summary>
        /// <param name="selectedDay">The day to select</param>
        /// <returns>List of WeekDay objects with the specified day selected</returns>
        public static List<WeekDay> CreateWeekDaysListWithSelection(WeekDayType selectedDay)
        {
            var weekDays = CreateWeekDaysList();
            var dayToSelect = weekDays.FirstOrDefault(d => d.WeekDayType == selectedDay);
            if (dayToSelect != null)
            {
                dayToSelect.IsSelected = true;
            }
            return weekDays;
        }

        /// <summary>
        /// Creates a list of week days with multiple days selected
        /// </summary>
        /// <param name="selectedDays">The days to select</param>
        /// <returns>List of WeekDay objects with the specified days selected</returns>
        public static List<WeekDay> CreateWeekDaysListWithSelection(IEnumerable<WeekDayType> selectedDays)
        {
            var weekDays = CreateWeekDaysList();
            foreach (var selectedDay in selectedDays)
            {
                var dayToSelect = weekDays.FirstOrDefault(d => d.WeekDayType == selectedDay);
                if (dayToSelect != null)
                {
                    dayToSelect.IsSelected = true;
                }
            }
            return weekDays;
        }

        /// <summary>
        /// Creates a list of week days based on current day of week
        /// </summary>
        /// <param name="currentDay">The current day of week</param>
        /// <returns>List of WeekDay objects with the current day selected</returns>
        public static List<WeekDay> CreateWeekDaysListWithCurrentDay(DayOfWeek currentDay)
        {
            var weekDayType = WeekDayExtensions.FromDayOfWeek(currentDay);
            return CreateWeekDaysListWithSelection(weekDayType);
        }
    }
}
