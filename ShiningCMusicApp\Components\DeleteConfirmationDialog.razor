@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Buttons
@using ShiningCMusicApp.Services.Interfaces
@inject IDialogService DialogService
@implements IDisposable

<SfDialog @bind-Visible="IsVisible" Header="@Title" Width="400px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="false" CssClass="delete-confirmation-dialog">
    <DialogEvents Closed="OnDialogClosed"></DialogEvents>
    <DialogTemplates>
        <Content>
            <div class="delete-confirmation-content">
                <div class="d-flex align-items-start mb-3">
                    <i class="bi bi-exclamation-triangle-fill text-warning me-3" style="font-size: 2rem;"></i>
                    <div>
                        <p class="mb-2 fw-semibold">@Message</p>
                        @if (!string.IsNullOrEmpty(Details))
                        {
                            <p class="text-muted small mb-0">@Details</p>
                        }
                    </div>
                </div>
            </div>
        </Content>
    </DialogTemplates>
    <DialogButtons>
        <DialogButton OnClick="OnConfirmClick" Content="@ConfirmButtonText" CssClass="btn btn-danger me-2" IsPrimary="false"></DialogButton>
        <DialogButton OnClick="OnCancelClick" Content="@CancelButtonText" CssClass="btn btn-cancel-custom"></DialogButton>
    </DialogButtons>
</SfDialog>

@code {
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public EventCallback<bool> IsVisibleChanged { get; set; }
    public string Title { get; set; } = "Confirm Delete";
    public string Message { get; set; } = "Are you sure you want to delete this item?";
    public string? Details { get; set; }
    public string ConfirmButtonText { get; set; } = "Delete";
    public string CancelButtonText { get; set; } = "Cancel";
    [Parameter] public EventCallback<bool> OnResult { get; set; }

    private TaskCompletionSource<bool>? _taskCompletionSource;

    protected override void OnInitialized()
    {
        if (DialogService is ShiningCMusicApp.Services.DialogService service)
        {
            service.RegisterDialog(this);
        }
    }

    public Task<bool> ShowAsync()
    {
        _taskCompletionSource = new TaskCompletionSource<bool>();
        IsVisible = true;
        InvokeAsync(StateHasChanged);
        return _taskCompletionSource.Task;
    }

    private async Task OnConfirmClick()
    {
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(false);
        await OnResult.InvokeAsync(true);
        _taskCompletionSource?.SetResult(true);
    }

    private async Task OnCancelClick()
    {
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(false);
        await OnResult.InvokeAsync(false);
        _taskCompletionSource?.SetResult(false);
    }

    private async Task OnDialogClosed()
    {
        IsVisible = false;
        await IsVisibleChanged.InvokeAsync(false);
        await OnResult.InvokeAsync(false);
        _taskCompletionSource?.SetResult(false);
    }

    public void Dispose()
    {
        if (DialogService is ShiningCMusicApp.Services.DialogService service)
        {
            service.UnregisterDialog(this);
        }
    }
}
