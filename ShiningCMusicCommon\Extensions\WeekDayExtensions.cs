using ShiningCMusicCommon.Enums;
using ShiningCMusicCommon.Constants;

namespace ShiningCMusicCommon.Extensions
{
    /// <summary>
    /// Extension methods for WeekDayType enum
    /// </summary>
    public static class WeekDayExtensions
    {
        /// <summary>
        /// Converts WeekDayType enum to RFC 5545 compliant code
        /// </summary>
        public static string ToCode(this WeekDayType weekDay)
        {
            return weekDay switch
            {
                WeekDayType.Monday => RecurrenceConstants.WeekDayCodes.Monday,
                WeekDayType.Tuesday => RecurrenceConstants.WeekDayCodes.Tuesday,
                WeekDayType.Wednesday => RecurrenceConstants.WeekDayCodes.Wednesday,
                WeekDayType.Thursday => RecurrenceConstants.WeekDayCodes.Thursday,
                WeekDayType.Friday => RecurrenceConstants.WeekDayCodes.Friday,
                WeekDayType.Saturday => RecurrenceConstants.WeekDayCodes.Saturday,
                WeekDayType.Sunday => RecurrenceConstants.WeekDayCodes.Sunday,
                _ => RecurrenceConstants.WeekDayCodes.Monday
            };
        }

        /// <summary>
        /// Converts WeekDayType enum to display name
        /// </summary>
        public static string ToDisplayName(this WeekDayType weekDay)
        {
            return weekDay switch
            {
                WeekDayType.Monday => RecurrenceConstants.WeekDayNames.Monday,
                WeekDayType.Tuesday => RecurrenceConstants.WeekDayNames.Tuesday,
                WeekDayType.Wednesday => RecurrenceConstants.WeekDayNames.Wednesday,
                WeekDayType.Thursday => RecurrenceConstants.WeekDayNames.Thursday,
                WeekDayType.Friday => RecurrenceConstants.WeekDayNames.Friday,
                WeekDayType.Saturday => RecurrenceConstants.WeekDayNames.Saturday,
                WeekDayType.Sunday => RecurrenceConstants.WeekDayNames.Sunday,
                _ => RecurrenceConstants.WeekDayNames.Monday
            };
        }

        /// <summary>
        /// Converts RFC 5545 code to WeekDayType enum
        /// </summary>
        public static WeekDayType FromCode(string code)
        {
            return code?.ToUpper() switch
            {
                RecurrenceConstants.WeekDayCodes.Monday => WeekDayType.Monday,
                RecurrenceConstants.WeekDayCodes.Tuesday => WeekDayType.Tuesday,
                RecurrenceConstants.WeekDayCodes.Wednesday => WeekDayType.Wednesday,
                RecurrenceConstants.WeekDayCodes.Thursday => WeekDayType.Thursday,
                RecurrenceConstants.WeekDayCodes.Friday => WeekDayType.Friday,
                RecurrenceConstants.WeekDayCodes.Saturday => WeekDayType.Saturday,
                RecurrenceConstants.WeekDayCodes.Sunday => WeekDayType.Sunday,
                _ => WeekDayType.Monday
            };
        }

        /// <summary>
        /// Converts DayOfWeek to WeekDayType
        /// </summary>
        public static WeekDayType FromDayOfWeek(DayOfWeek dayOfWeek)
        {
            return dayOfWeek switch
            {
                DayOfWeek.Monday => WeekDayType.Monday,
                DayOfWeek.Tuesday => WeekDayType.Tuesday,
                DayOfWeek.Wednesday => WeekDayType.Wednesday,
                DayOfWeek.Thursday => WeekDayType.Thursday,
                DayOfWeek.Friday => WeekDayType.Friday,
                DayOfWeek.Saturday => WeekDayType.Saturday,
                DayOfWeek.Sunday => WeekDayType.Sunday,
                _ => WeekDayType.Monday
            };
        }

        /// <summary>
        /// Converts WeekDayType to DayOfWeek
        /// </summary>
        public static DayOfWeek ToDayOfWeek(this WeekDayType weekDay)
        {
            return weekDay switch
            {
                WeekDayType.Monday => DayOfWeek.Monday,
                WeekDayType.Tuesday => DayOfWeek.Tuesday,
                WeekDayType.Wednesday => DayOfWeek.Wednesday,
                WeekDayType.Thursday => DayOfWeek.Thursday,
                WeekDayType.Friday => DayOfWeek.Friday,
                WeekDayType.Saturday => DayOfWeek.Saturday,
                WeekDayType.Sunday => DayOfWeek.Sunday,
                _ => DayOfWeek.Monday
            };
        }
    }
}
