using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;
using ShiningCMusicCommon.Utilities;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;

        public UserController(IUserService userService)
        {
            _userService = userService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<User>>> GetUsers()
        {
            try
            {
                var users = await _userService.GetUsersAsync();
                return Ok(users);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpGet("{loginName}")]
        public async Task<ActionResult<User>> GetUser(string loginName)
        {
            try
            {
                var user = await _userService.GetUserAsync(loginName);
                if (user == null)
                {
                    return NotFound();
                }
                return Ok(user);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPost]
        public async Task<ActionResult<User>> CreateUser(User user)
        {
            try
            {
                // Validate password
                if (!string.IsNullOrEmpty(user.Password) && !PasswordValidator.IsValidPassword(user.Password))
                {
                    return BadRequest($"Invalid password. {PasswordValidator.GetPasswordRequirements()}");
                }

                var createdUser = await _userService.CreateUserAsync(user);
                return CreatedAtAction(nameof(GetUser), new { loginName = createdUser.LoginName }, createdUser);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPut("{loginName}")]
        public async Task<IActionResult> UpdateUser(string loginName, User user)
        {
            try
            {
                // Validate password if it's being updated
                if (!string.IsNullOrEmpty(user.Password) && !PasswordValidator.IsValidPassword(user.Password))
                {
                    return BadRequest($"Invalid password. {PasswordValidator.GetPasswordRequirements()}");
                }

                var success = await _userService.UpdateUserAsync(loginName, user);
                if (!success)
                {
                    return NotFound();
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpDelete("{loginName}")]
        public async Task<IActionResult> DeleteUser(string loginName)
        {
            try
            {
                var success = await _userService.DeleteUserAsync(loginName);
                if (!success)
                {
                    return NotFound();
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPost("authenticate")]
        [AllowAnonymous]
        public async Task<ActionResult<User>> Authenticate([FromBody] LoginRequest request)
        {
            try
            {
                var user = await _userService.AuthenticateAsync(request.LoginName, request.Password);
                if (user == null)
                {
                    return Unauthorized("Invalid login credentials");
                }
                return Ok(user);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpGet("roles")]
        public async Task<ActionResult<IEnumerable<UserRole>>> GetUserRoles()
        {
            try
            {
                var roles = await _userService.GetUserRolesAsync();
                return Ok(roles);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPost("roles")]
        public async Task<ActionResult<UserRole>> CreateUserRole(UserRole role)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(role.Description))
                {
                    return BadRequest("Role description is required");
                }

                var createdRole = await _userService.CreateUserRoleAsync(role);
                if (createdRole == null)
                {
                    return BadRequest("Failed to create user role");
                }
                return CreatedAtAction(nameof(GetUserRoles), new { id = createdRole.ID }, createdRole);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPut("roles/{id}")]
        public async Task<IActionResult> UpdateUserRole(int id, UserRole role)
        {
            try
            {
                if (id != role.ID)
                {
                    return BadRequest("Role ID mismatch");
                }

                // Validate input
                if (string.IsNullOrWhiteSpace(role.Description))
                {
                    return BadRequest("Role description is required");
                }

                var success = await _userService.UpdateUserRoleAsync(role);
                if (!success)
                {
                    return NotFound();
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpDelete("roles/{id}")]
        public async Task<IActionResult> DeleteUserRole(int id)
        {
            try
            {
                var success = await _userService.DeleteUserRoleAsync(id);
                if (!success)
                {
                    return NotFound();
                }
                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPost("migrate-passwords")]
        public async Task<IActionResult> MigratePasswords()
        {
            try
            {
                await _userService.MigratePasswordsToHashedAsync();
                return Ok(new { message = "Password migration completed successfully" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }
    }

    public class LoginRequest
    {
        public string LoginName { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }
}
