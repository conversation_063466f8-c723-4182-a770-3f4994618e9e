using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public class User
    {
        [Key]
        [StringLength(20)]
        public string LoginName { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string? UserName { get; set; }
        
        [StringLength(100)] // Increased for hashed passwords
        public string? Password { get; set; }
        
        [StringLength(100)]
        public string? Note { get; set; }
        
        public int? RoleId { get; set; }

        // Additional properties for API responses
        public string? RoleDescription { get; set; }

        // Navigation properties
        public virtual UserRole? Role { get; set; }
    }
}
