using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public class Subject
    {
        public int SubjectId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string SubjectName { get; set; } = string.Empty;
        
        // Navigation properties
        public virtual ICollection<Lesson> Lessons { get; set; } = new List<Lesson>();
    }
}
