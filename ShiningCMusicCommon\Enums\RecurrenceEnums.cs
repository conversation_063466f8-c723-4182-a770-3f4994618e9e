namespace ShiningCMusicCommon.Enums
{
    /// <summary>
    /// Enum representing recurrence types for recurring lessons
    /// </summary>
    public enum RecurrenceType
    {
        /// <summary>
        /// Daily recurrence pattern
        /// </summary>
        Daily,
        
        /// <summary>
        /// Weekly recurrence pattern
        /// </summary>
        Weekly,
        
        /// <summary>
        /// Monthly recurrence pattern
        /// </summary>
        Monthly
    }

    /// <summary>
    /// Enum representing days of the week with RFC 5545 compliant codes
    /// </summary>
    public enum WeekDayType
    {
        /// <summary>
        /// Monday
        /// </summary>
        Monday,
        
        /// <summary>
        /// Tuesday
        /// </summary>
        Tuesday,
        
        /// <summary>
        /// Wednesday
        /// </summary>
        Wednesday,
        
        /// <summary>
        /// Thursday
        /// </summary>
        Thursday,
        
        /// <summary>
        /// Friday
        /// </summary>
        Friday,
        
        /// <summary>
        /// Saturday
        /// </summary>
        Saturday,
        
        /// <summary>
        /// Sunday
        /// </summary>
        Sunday
    }
}
