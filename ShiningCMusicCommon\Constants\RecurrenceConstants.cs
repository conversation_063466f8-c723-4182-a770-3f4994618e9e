using ShiningCMusicCommon.Enums;

namespace ShiningCMusicCommon.Constants
{
    /// <summary>
    /// Constants for recurrence functionality
    /// </summary>
    public static class RecurrenceConstants
    {
        /// <summary>
        /// Recurrence type string values for UI binding
        /// </summary>
        public static class RecurrenceTypeValues
        {
            public const string Daily = "daily";
            public const string Weekly = "weekly";
            public const string Monthly = "monthly";
        }

        /// <summary>
        /// Week day codes following RFC 5545 standard
        /// </summary>
        public static class WeekDayCodes
        {
            public const string Monday = "MO";
            public const string Tuesday = "TU";
            public const string Wednesday = "WE";
            public const string Thursday = "TH";
            public const string Friday = "FR";
            public const string Saturday = "SA";
            public const string Sunday = "SU";
        }

        /// <summary>
        /// Week day display names
        /// </summary>
        public static class WeekDayNames
        {
            public const string Monday = "Mon";
            public const string Tuesday = "Tue";
            public const string Wednesday = "Wed";
            public const string Thursday = "Thu";
            public const string Friday = "Fri";
            public const string Saturday = "Sat";
            public const string Sunday = "Sun";
        }

        /// <summary>
        /// Default values for recurrence settings
        /// </summary>
        public static class Defaults
        {
            public const RecurrenceType DefaultRecurrenceType = RecurrenceType.Weekly;
            public const string DefaultRecurrenceTypeValue = RecurrenceTypeValues.Weekly;
            public const int DefaultInterval = 1;
            public const int DefaultCount = 10;
            public const int MinInterval = 1;
            public const int MaxInterval = 99;
            public const int MinCount = 1;
            public const int MaxCount = 365;
        }


    }
}
