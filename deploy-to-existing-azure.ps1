# Deploy Shining C Music App to Existing Azure App Service
# Run this script from the repository root directory

param(
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory=$true)]
    [string]$AppServiceName,
    
    [Parameter(Mandatory=$true)]
    [string]$SubscriptionId
)

# Set subscription if provided
if ($SubscriptionId) {
    Write-Host "Setting Azure subscription..." -ForegroundColor Yellow
    az account set --subscription $SubscriptionId
}

Write-Host "Deploying Shining C Music App to existing Azure App Service: $AppServiceName" -ForegroundColor Green

# Get the App Service URL
$AppServiceUrl = "https://$AppServiceName.azurewebsites.net"
Write-Host "Target URL: $AppServiceUrl" -ForegroundColor Cyan

# Step 1: Update App Service Configuration
Write-Host "Updating App Service configuration..." -ForegroundColor Yellow
az webapp config appsettings set --resource-group $ResourceGroupName --name $AppServiceName --settings `
    "DATABASE_CONNECTION_STRING=Server=tcp:shining.database.windows.net,1433;Initial Catalog=MusicSchool;Persist Security Info=False;User ID=sam;Password=********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;" `
    "ApiBaseUrl=$AppServiceUrl/" `
    "API_BASE_URL=$AppServiceUrl/" `
    "ASPNETCORE_ENVIRONMENT=Production"

# Step 2: Update Blazor app configuration for production
Write-Host "Updating Blazor app configuration..." -ForegroundColor Yellow
$BlazorConfigPath = "ShiningCMusicApp/wwwroot/appsettings.json"
$BlazorConfig = @{
    "SyncfusionLicense" = "MzkxNzI4MkAzMjM5MmUzMDJlMzAzYjMyMzkzYmlCSHVjUFNHMVcwWDI0Mm5reFc2M21MbXA4cEFVcWRRWXl1eFFUWnlXYTA9"
    "ApiBaseUrl" = "$AppServiceUrl/api"
} | ConvertTo-Json -Depth 10

Set-Content -Path $BlazorConfigPath -Value $BlazorConfig -Encoding UTF8

# Step 3: Clean and restore packages
Write-Host "Cleaning and restoring packages..." -ForegroundColor Yellow
dotnet clean
dotnet restore

# Step 4: Build the solution
Write-Host "Building the solution in Release mode..." -ForegroundColor Yellow
dotnet build --configuration Release --no-restore

if ($LASTEXITCODE -ne 0) {
    Write-Error "Build failed. Please check the errors above."
    exit 1
}

# Step 5: Publish the API project (which includes the Blazor app)
Write-Host "Publishing the application..." -ForegroundColor Yellow
$PublishPath = "publish"
if (Test-Path $PublishPath) {
    Remove-Item -Recurse -Force $PublishPath
}

dotnet publish ShiningCMusicApi/ShiningCMusicApi.csproj --configuration Release --output $PublishPath --no-build

if ($LASTEXITCODE -ne 0) {
    Write-Error "Publish failed. Please check the errors above."
    exit 1
}

# Step 6: Create deployment package
Write-Host "Creating deployment package..." -ForegroundColor Yellow
$ZipPath = "$PublishPath.zip"
if (Test-Path $ZipPath) {
    Remove-Item $ZipPath
}

# Create zip file
if ($IsWindows -or $env:OS -eq "Windows_NT" -or $PSVersionTable.Platform -eq "Win32NT") {
    Compress-Archive -Path "$PublishPath\*" -DestinationPath $ZipPath
} else {
    # Use zip command on Linux/Mac
    Push-Location $PublishPath
    zip -r "../$PublishPath.zip" .
    Pop-Location
}

# Step 7: Deploy to Azure App Service
Write-Host "Deploying to Azure App Service..." -ForegroundColor Yellow
az webapp deployment source config-zip --resource-group $ResourceGroupName --name $AppServiceName --src $ZipPath

if ($LASTEXITCODE -ne 0) {
    Write-Error "Deployment failed. Please check the errors above."
    exit 1
}

# Step 8: Clean up temporary files
Write-Host "Cleaning up temporary files..." -ForegroundColor Yellow
Remove-Item -Recurse -Force $PublishPath
Remove-Item $ZipPath

# Step 9: Restart the app service to ensure new settings take effect
Write-Host "Restarting App Service..." -ForegroundColor Yellow
az webapp restart --resource-group $ResourceGroupName --name $AppServiceName

Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host "Your application is available at: $AppServiceUrl" -ForegroundColor Cyan
Write-Host "API endpoints are available at: $AppServiceUrl/api" -ForegroundColor Cyan
Write-Host "Swagger UI (if enabled): $AppServiceUrl/swagger" -ForegroundColor Cyan

# Optional: Open the application in browser
$OpenBrowser = Read-Host "Would you like to open the application in your browser? (y/n)"
if ($OpenBrowser -eq "y" -or $OpenBrowser -eq "Y") {
    Start-Process $AppServiceUrl
}
