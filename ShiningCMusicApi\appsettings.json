{
  "ConnectionStrings": {
    "MusicSchool": "data source=localhost;initial catalog=MusicSchool;persist security info=True;user id=sa;password=**********;MultipleActiveResultSets=True;Encrypt=false;"
  },
  "ApiBaseUrl": "https://localhost:7268/",
  "SyncfusionLicense": "MzkxNzI4MkAzMjM5MmUzMDJlMzAzYjMyMzkzYmlCSHVjUFNHMVcwWDI0Mm5reFc2M21MbXA4cEFVcWRRWXl1eFFUWnlXYTA9",
  "LessonCleanup": {
    "IntervalHours": 24,
    "RetentionDays": 30
  },
  "SessionTimeout": {
    "TimeoutMinutes": 30
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*"
}

  //"ConnectionStrings": {
  //  "MusicSchool": "Server=tcp:shining.database.windows.net,1433;Initial Catalog=MusicSchool;Persist Security Info=False;User ID=sam;Password=********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
  //},
