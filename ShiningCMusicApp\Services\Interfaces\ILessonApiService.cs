using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services
{
    public interface ILessonApiService
    {
        Task<List<ScheduleEvent>> GetLessonsAsync();
        Task<ScheduleEvent?> CreateLessonAsync(ScheduleEvent lesson);
        Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson);
        Task<bool> UpdateLessonAsync(ScheduleEvent lesson);
        Task<bool> DeleteLessonAsync(int id);
    }
}
