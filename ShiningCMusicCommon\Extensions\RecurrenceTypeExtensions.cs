using ShiningCMusicCommon.Enums;
using ShiningCMusicCommon.Constants;

namespace ShiningCMusicCommon.Extensions
{
    /// <summary>
    /// Extension methods for RecurrenceType enum
    /// </summary>
    public static class RecurrenceTypeExtensions
    {
        /// <summary>
        /// Converts RecurrenceType enum to string value for UI binding
        /// </summary>
        public static string ToStringValue(this RecurrenceType recurrenceType)
        {
            return recurrenceType switch
            {
                RecurrenceType.Daily => RecurrenceConstants.RecurrenceTypeValues.Daily,
                RecurrenceType.Weekly => RecurrenceConstants.RecurrenceTypeValues.Weekly,
                RecurrenceType.Monthly => RecurrenceConstants.RecurrenceTypeValues.Monthly,
                _ => RecurrenceConstants.RecurrenceTypeValues.Weekly
            };
        }

        /// <summary>
        /// Converts string value to RecurrenceType enum
        /// </summary>
        public static RecurrenceType FromStringValue(string value)
        {
            return value?.ToLower() switch
            {
                RecurrenceConstants.RecurrenceTypeValues.Daily => RecurrenceType.Daily,
                RecurrenceConstants.RecurrenceTypeValues.Weekly => RecurrenceType.Weekly,
                RecurrenceConstants.RecurrenceTypeValues.Monthly => RecurrenceType.Monthly,
                _ => RecurrenceType.Weekly
            };
        }
    }
}
