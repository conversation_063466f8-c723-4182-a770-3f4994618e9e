using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Timers;

namespace ShiningCMusicApp.Services
{
    public interface ISessionTimeoutService
    {
        Task InitializeAsync();
        Task ResetTimeoutAsync();
        Task StartTimeoutAsync();
        Task StopTimeoutAsync();
        event Func<Task>? OnSessionTimeout;
        bool IsActive { get; }
    }

    public class SessionTimeoutService : ISessionTimeoutService, IDisposable
    {
        private readonly IJSRuntime _jsRuntime;
        private readonly CustomAuthenticationStateProvider _authStateProvider;
        private readonly NavigationManager _navigationManager;
        private System.Timers.Timer? _timeoutTimer;
        private readonly int _timeoutMinutes;
        private bool _isActive = false;
        private bool _disposed = false;

        public event Func<Task>? OnSessionTimeout;
        public bool IsActive => _isActive;

        public SessionTimeoutService(
            IJSRuntime jsRuntime,
            CustomAuthenticationStateProvider authStateProvider,
            NavigationManager navigationManager,
            int timeoutMinutes = 30)
        {
            _jsRuntime = jsRuntime;
            _authStateProvider = authStateProvider;
            _navigationManager = navigationManager;
            _timeoutMinutes = timeoutMinutes;
        }

        public async Task InitializeAsync()
        {
            try
            {
                // Initialize JavaScript activity tracking
                await _jsRuntime.InvokeVoidAsync("sessionTimeout.initialize",
                    DotNetObjectReference.Create(this));

                Console.WriteLine($"Session timeout initialized with {_timeoutMinutes} minutes timeout");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize session timeout: {ex.Message}");
            }
        }

        public async Task StartTimeoutAsync()
        {
            if (_disposed) return;

            await StopTimeoutAsync();

            _timeoutTimer = new System.Timers.Timer(_timeoutMinutes * 60 * 1000); // Convert minutes to milliseconds
            _timeoutTimer.Elapsed += OnTimeoutElapsed;
            _timeoutTimer.AutoReset = false;
            _timeoutTimer.Start();
            _isActive = true;

            Console.WriteLine($"Session timeout started - will expire in {_timeoutMinutes} minutes");
        }

        public async Task StopTimeoutAsync()
        {
            if (_timeoutTimer != null)
            {
                _timeoutTimer.Stop();
                _timeoutTimer.Dispose();
                _timeoutTimer = null;
            }
            _isActive = false;
            await Task.CompletedTask;
        }

        public async Task ResetTimeoutAsync()
        {
            if (_disposed) return;

            // Check if user is still authenticated before resetting
            var authState = await _authStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                await StartTimeoutAsync();
                Console.WriteLine("Session timeout reset due to user activity");
            }
        }

        [JSInvokable]
        public async Task OnUserActivity()
        {
            await ResetTimeoutAsync();
        }

        private async void OnTimeoutElapsed(object? sender, ElapsedEventArgs e)
        {
            try
            {
                Console.WriteLine("Session timeout elapsed - logging out user");
                
                // Stop the timer
                await StopTimeoutAsync();
                
                // Trigger the timeout event
                if (OnSessionTimeout != null)
                {
                    await OnSessionTimeout.Invoke();
                }
                
                // Logout the user
                await _authStateProvider.LogoutAsync();
                
                // Navigate to login page
                _navigationManager.NavigateTo("/login", true);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during session timeout: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _timeoutTimer?.Dispose();
                _disposed = true;
            }
            GC.SuppressFinalize(this);
        }
    }
}
