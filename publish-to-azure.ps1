# Publish Shining C Music App to Azure App Service
# Run this script from the repository root directory

param(
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory=$true)]
    [string]$AppServiceName
)

Write-Host "Publishing Shining C Music App to Azure..." -ForegroundColor Green

# Get the App Service URL for configuration
$AppServiceUrl = "https://$AppServiceName.azurewebsites.net"

# Update Blazor app configuration for production
Write-Host "Updating Blazor app configuration..." -ForegroundColor Yellow
$BlazorConfigPath = "ShiningCMusicApp/wwwroot/appsettings.json"
$BlazorConfig = @{
    "SyncfusionLicense" = "MzkxNzI4MkAzMjM5MmUzMDJlMzAzYjMyMzkzYmlCSHVjUFNHMVcwWDI0Mm5reFc2M21MbXA4cEFVcWRRWXl1eFFUWnlXYTA9"
    "ApiBaseUrl" = "$AppServiceUrl/api"
} | ConvertTo-Json -Depth 10

Set-Content -Path $BlazorConfigPath -Value $BlazorConfig

# Clean and restore packages
Write-Host "Cleaning and restoring packages..." -ForegroundColor Yellow
dotnet clean
dotnet restore

# Build the solution
Write-Host "Building the solution..." -ForegroundColor Yellow
dotnet build --configuration Release

# Publish the API project (which includes the Blazor app)
Write-Host "Publishing the application..." -ForegroundColor Yellow
$PublishPath = "publish"
dotnet publish ShiningCMusicApi/ShiningCMusicApi.csproj --configuration Release --output $PublishPath

# Deploy to Azure App Service
Write-Host "Deploying to Azure App Service..." -ForegroundColor Yellow
az webapp deployment source config-zip --resource-group $ResourceGroupName --name $AppServiceName --src "$PublishPath.zip"

# Create zip file for deployment
Write-Host "Creating deployment package..." -ForegroundColor Yellow
if (Test-Path "$PublishPath.zip") {
    Remove-Item "$PublishPath.zip"
}

# Use PowerShell to create zip (works on Windows)
if ($IsWindows -or $env:OS -eq "Windows_NT") {
    Compress-Archive -Path "$PublishPath\*" -DestinationPath "$PublishPath.zip"
} else {
    # Use zip command on Linux/Mac
    Set-Location $PublishPath
    zip -r "../$PublishPath.zip" .
    Set-Location ..
}

# Deploy the zip file
az webapp deployment source config-zip --resource-group $ResourceGroupName --name $AppServiceName --src "$PublishPath.zip"

# Clean up
Remove-Item -Recurse -Force $PublishPath
Remove-Item "$PublishPath.zip"

Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host "Your application is available at: $AppServiceUrl" -ForegroundColor Cyan
Write-Host "API endpoints are available at: $AppServiceUrl/api" -ForegroundColor Cyan
